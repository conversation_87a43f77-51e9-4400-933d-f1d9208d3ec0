// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               Int               @id @default(autoincrement())
  username         String            @unique
  email            String            @unique
  password         String?
  resetToken       String?
  resetTokenExpiry DateTime?
  createdAt        DateTime?         @default(now())
  updatedAt        DateTime?         @updatedAt
  isPasswordReset  Boolean?          @default(true)
  // relation
  isAdmin          <PERSON>an           @default(false)
  companies        Company[] // A user can create multiple companies
  sharedCompanies  SharedCompanies[]

  sharedByDefaultSettings   DefaultUserSettings[] @relation("SharedBy")
  sharedWithDefaultSettings DefaultUserSettings[] @relation("SharedWith")

  TrialBalanceReport TrialBalanceReport[]

  ProfitLossReport ProfitLossReport[]

  BalanceSheetReport BalanceSheetReport[]

  AccountReceivableAgingSummaryReport AccountReceivableAgingSummaryReport[]

  AccountPayableAgingSummaryReport AccountPayableAgingSummaryReport[]

  Account Account[]

  // Template settings relations
  templateSettings       TemplateSettings[]      @relation("UserTemplateSettings")
  updatedSettings        TemplateSettings[]      @relation("UpdatedSettings")
  updatedContentSettings ReportContentSettings[] @relation("UpdatedByUser")
}

model Company {
  id                             Int                     @id @default(autoincrement())
  name                           String?
  fiscal_year_end                DateTime?               @db.Date
  naics                          String?
  description                    String?
  logo                           String?
  country                        String?
  state                          String?
  city                           String?
  userId                         Int?
  createdAt                      DateTime?               @default(now())
  updatedAt                      DateTime?               @updatedAt
  market                         String?
  qboAccessToken                 String?
  qboAccessTokenCreatedAt        DateTime?
  qboCompanyName                 String?
  qboConnectionStatus            QboConnectionStatus     @default(DISCONNECTED)
  qboRealmID                     String?
  qboRefreshToken                String                  @default("null")
  qboTokenExpiryAt               DateTime?               @db.Timestamp(6)
  tokenExpiryAtUtcDateTime       DateTime?
  APReportLastSyncDate           DateTime?               @db.Date
  ARReportLastSyncDate           DateTime?               @db.Date
  BalanceSheetReportLastSyncDate DateTime?               @db.Date
  ProfitLossReportLastSyncDate   DateTime?               @db.Date
  TransactionReportLastSyncDate  DateTime?               @db.Date
  TrialBalanceReportLastSyncDate DateTime?               @db.Date
  User                           User?                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  companyFiles                   Document[]
  companyReports                 ReportRequest[]
  sharedCompanies                SharedCompanies[]
  reportContentSettings          ReportContentSettings[]
}

enum FileType {
  CHART_OF_ACCOUNTS
  TRIAL_BALANCE
  CASH_FLOW
  NON_CASH_FLOW
  TEMPLATE
}

model Document {
  id            Int            @id @default(autoincrement())
  file_type     FileType?
  file_name     String?
  file_key      String?
  size          String?
  companyId     Int?
  requestId     Int? // Foreign key to reference the Report
  templateId    Int? // Foreign key to reference the Template
  createdAt     DateTime?      @default(now())
  updatedAt     DateTime?      @updatedAt
  Company       Company?       @relation(fields: [companyId], references: [id])
  Templates     Templates?     @relation(fields: [templateId], references: [id], onDelete: Cascade)
  ReportRequest ReportRequest? @relation(fields: [requestId], references: [id], onDelete: Cascade) // Relationship with Report
}

enum ReportType {
  AP
  AR
  TRANSACTION
}

enum ReportStatus {
  pending
  processing
  download
  edit
  error
}

model ReportRequest {
  id             Int           @id @default(autoincrement())
  name           String
  request_type   String
  date_requested DateTime
  companyId      Int?
  status         ReportStatus?
  cashflow       Boolean
  Company        Company?      @relation(fields: [companyId], references: [id])
  documents      Document[]
  createdAt      DateTime?     @default(now())
  updatedAt      DateTime?
  text           String?
}

model Templates {
  id          Int        @id @default(autoincrement())
  name        String
  description String?
  documents   Document[]
  createdAt   DateTime?  @default(now())
  updatedAt   DateTime?  @updatedAt
}

model Faqs {
  id        Int       @id @default(autoincrement())
  question  String
  answer    String
  createdAt DateTime? @default(now())
  updatedAt DateTime? @updatedAt
}

model SharedCompanies {
  id        Int      @id @default(autoincrement())
  userId    Int?
  companyId Int?
  companies Company? @relation(fields: [companyId], references: [id])
  users     User?    @relation(fields: [userId], references: [id])

  @@unique([userId, companyId])
}

model DefaultUserSettings {
  id             Int       @id @default(autoincrement())
  username       String
  sharedBy       Int?
  sharedWith     Int?
  deleted        Boolean?  @default(false)
  createdAt      DateTime? @default(now())
  updatedAt      DateTime? @updatedAt
  sharedByUser   User?     @relation("SharedBy", fields: [sharedBy], references: [id], onDelete: Cascade)
  sharedWithUser User?     @relation("SharedWith", fields: [sharedWith], references: [id], onDelete: Cascade)

  @@unique([sharedBy, sharedWith])
}

model Account {
  id                        Int      @id @default(autoincrement())
  userId                    Int
  realmId                   String   @db.VarChar(100)
  accountId                 String   @db.VarChar(100)
  connectionId              Int?
  name                      String   @db.VarChar(255)
  fullyQualifiedAccountName String   @db.VarChar(500)
  type                      String   @db.VarChar(100)
  isActiveAccount           Boolean?
  currencyCode              String?  @db.VarChar(10)
  accountSubTypeName        String?  @db.VarChar(100)
  accountClassification     String?  @db.VarChar(100)
  currentAccountBalance     Decimal? @db.Decimal(15, 2)
  isSubAccountFlag          Boolean?
  parentAccountQuickbooksId String?  @db.VarChar(100)
  createdAt                 DateTime @default(now())
  modifiedAt                DateTime @default(now()) @updatedAt
  User                      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relations to financial reports
  profitLossReports   ProfitLossReport[]
  trialBalanceReports TrialBalanceReport[]
  balanceSheetReports BalanceSheetReport[]

  @@unique([accountId, userId, realmId])
  @@index([userId])
  @@index([realmId])
  @@index([accountId])
  @@index([connectionId])
  @@index([type])
  @@map("Account")
}

model TemplateSettings {
  id            Int            @id @default(autoincrement())
  userId        Int? // NULL for global admin settings, populated for user custom settings
  reportType    ReportTemplate
  templateType  TemplateType   @default(GLOBAL) // GLOBAL (admin managed) or CUSTOM (user managed)
  settings      Json
  updatedBy     Int?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  user          User?          @relation("UserTemplateSettings", fields: [userId], references: [id], onDelete: Cascade)
  updatedByUser User?          @relation("UpdatedSettings", fields: [updatedBy], references: [id], onDelete: SetNull)

  @@unique([userId, reportType, templateType]) // Ensures one setting per user per report type per template type
  @@index([userId])
  @@index([templateType])
  @@index([reportType])
  @@map("TemplateSettings")
}

model ReportContentSettings {
  id                Int            @id @default(autoincrement())
  companyId         Int // Required field for company-specific settings
  reportType        ReportTemplate
  chartSettings     Json // e.g. { "ROE_ROA": true, "PNL": false }
  promptDescription String // Description for the whole report
  updatedBy         Int? // User ID who last updated
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  // Relations
  company       Company @relation(fields: [companyId], references: [id], onDelete: Cascade)
  updatedByUser User?   @relation("UpdatedByUser", fields: [updatedBy], references: [id], onDelete: SetNull)

  @@unique([companyId, reportType]) // Ensures one setting per company per report type
  @@index([companyId])
  @@index([reportType])
  @@index([updatedBy])
  @@map("ReportContentSettings")
}

model AccountPayableAgingSummaryReport {
  id         Int      @id @default(autoincrement())
  userId     Int
  realmId    String   @db.VarChar(100)
  year       Int
  month      Int
  total      Decimal  @db.Decimal(15, 2)
  createdAt  DateTime @default(now())
  modifiedAt DateTime @default(now()) @updatedAt
  User       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, realmId, year, month])
  @@index([userId])
  @@index([realmId])
  @@index([year, month])
  @@map("AccountPayableAgingSummaryReport")
}

model AccountReceivableAgingSummaryReport {
  id         Int      @id @default(autoincrement())
  userId     Int
  realmId    String   @db.VarChar(100)
  year       Int
  month      Int
  total      Decimal  @db.Decimal(15, 2)
  createdAt  DateTime @default(now())
  modifiedAt DateTime @default(now()) @updatedAt
  User       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, realmId, year, month])
  @@index([userId])
  @@index([realmId])
  @@index([year, month])
  @@map("AccountReceivableAgingSummaryReport")
}

model ProfitLossReport {
  id           Int      @id @default(autoincrement())
  userId       Int
  realmId      String   @db.VarChar(100)
  accountId    String   @db.VarChar(100)
  year         Int
  month        Int
  amount       Decimal  @db.Decimal(15, 2)
  currencyCode String   @db.VarChar(10)
  createdAt    DateTime @default(now())
  modifiedAt   DateTime @default(now()) @updatedAt
  User         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  Account      Account? @relation(fields: [accountId, userId, realmId], references: [accountId, userId, realmId])

  @@unique([userId, realmId, year, month, accountId])
  @@index([userId])
  @@index([realmId])
  @@index([accountId])
  @@index([year, month])
  @@map("ProfitLossReport")
}

model BalanceSheetReport {
  id              Int      @id @default(autoincrement())
  userId          Int
  realmId         String   @db.VarChar(100)
  accountId       String   @db.VarChar(100)
  year            Int
  month           Int
  statementAmount Decimal  @db.Decimal(15, 2)
  currencyCode    String   @db.VarChar(10)
  createdAt       DateTime @default(now())
  modifiedAt      DateTime @default(now()) @updatedAt
  User            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  Account         Account? @relation(fields: [accountId, userId, realmId], references: [accountId, userId, realmId])

  @@unique([userId, realmId, year, month, accountId])
  @@index([userId])
  @@index([realmId])
  @@index([accountId])
  @@index([year, month])
  @@map("BalanceSheetReport")
}

model TrialBalanceReport {
  id                   Int      @id @default(autoincrement())
  userId               Int
  realmId              String   @db.VarChar(100)
  accountId            String   @db.VarChar(100)
  month                Int
  year                 Int
  monthEndDebitAmount  Decimal  @db.Decimal(15, 2)
  monthEndCreditAmount Decimal  @db.Decimal(15, 2)
  netChangeAmount      Decimal  @db.Decimal(15, 2)
  createdAt            DateTime @default(now())
  modifiedAt           DateTime @default(now()) @updatedAt
  User                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  Account              Account? @relation(fields: [accountId, userId, realmId], references: [accountId, userId, realmId])

  @@unique([userId, realmId, year, month, accountId])
  @@index([userId])
  @@index([realmId])
  @@index([accountId])
  @@index([year, month])
  @@map("TrialBalanceReport")
}

enum QboConnectionStatus {
  CONNECTED
  DISCONNECTED
}

enum ReportTemplate {
  DEEPSIGHT
}

enum TemplateType {
  GLOBAL
  CUSTOM
}
