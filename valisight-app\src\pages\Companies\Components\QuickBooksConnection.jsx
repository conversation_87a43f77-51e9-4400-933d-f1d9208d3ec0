import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Card,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  <PERSON>ack,
  Chip,
  CircularProgress,
  Fade,
  Slide,
  Paper,
  Divider,
  Alert,
  AlertTitle,
  LinearProgress,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  AccountBalance as AccountBalanceIcon,
  Security as SecurityIcon,
  Sync as SyncIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import qboButton from "../../../assets/C2QB_green_btn_med_default.svg";
import qboButtonHover from "../../../assets/C2QB_green_btn_med_hover.svg";

const QuickBooksConnection = ({
  qboConnected,
  qboLoading,
  qboStatus,
  company,
  onConnect,
  onDisconnect,
  onRefreshStatus,
  showDetailedView = false,
}) => {
  const [showDetails, setShowDetails] = useState(showDetailedView);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [showBenefits, setShowBenefits] = useState(!qboConnected);

  useEffect(() => {
    if (qboLoading) {
      const interval = setInterval(() => {
        setConnectionProgress((prev) => {
          if (prev >= 90) return 90;
          return prev + Math.random() * 10;
        });
      }, 200);
      return () => clearInterval(interval);
    } else {
      setConnectionProgress(qboConnected ? 100 : 0);
    }
  }, [qboLoading, qboConnected]);

  const connectionBenefits = [
    {
      icon: <SyncIcon sx={{ color: "primary.main" }} />,
      title: "Real-time Data Sync",
      description: "Automatically sync your financial data in real-time",
    },
    {
      icon: <AccountBalanceIcon sx={{ color: "success.main" }} />,
      title: "Complete Financial Reports",
      description:
        "Access trial balance, P&L, balance sheet, and aging reports",
    },
    {
      icon: <SecurityIcon sx={{ color: "warning.main" }} />,
      title: "Secure Integration",
      description: "Bank-level security with OAuth 2.0 authentication",
    },
  ];

  const renderConnectionStatus = () => {
    if (qboLoading) {
      return (
        <Stack direction="row" spacing={2} alignItems="center">
          <CircularProgress size={20} thickness={4} />
          <Typography variant="body2" color="text.secondary">
            Connecting to QuickBooks...
          </Typography>
        </Stack>
      );
    }

    return (
      <Stack direction="row" spacing={2} alignItems="center">
        {qboConnected ? (
          <>
            <CheckCircleIcon sx={{ color: "success.main", fontSize: 20 }} />
            <Chip
              label="Connected"
              size="small"
              sx={{
                backgroundColor: "success.light",
                color: "success.dark",
                fontWeight: 600,
              }}
            />
          </>
        ) : (
          <>
            <ErrorIcon sx={{ color: "error.main", fontSize: 20 }} />
            <Chip
              label="Not Connected"
              size="small"
              sx={{
                backgroundColor: "error.light",
                color: "error.dark",
                fontWeight: 600,
              }}
            />
          </>
        )}
      </Stack>
    );
  };

  const renderDetailedView = () => (
    <Slide direction="up" in={showDetails} timeout={400}>
      <Paper
        elevation={12}
        sx={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: { xs: "90%", sm: "80%", md: "600px" },
          maxHeight: "80vh",
          overflow: "auto",
          zIndex: 1300,
          borderRadius: 3,
        }}
      >
        <Box sx={{ p: 4 }}>
          {/* Header */}
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ mb: 3 }}
          >
            <Stack direction="row" spacing={2} alignItems="center">
              <Box
                component="img"
                src={qboButton}
                alt="QuickBooks"
                sx={{ height: 40, width: "auto" }}
              />
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography variant="h5" fontWeight={700}>
                  Connect Fintuition Company to
                </Typography>
                <Typography variant="h5" fontWeight={700} color="primary.main">
                  QuickBooks
                </Typography>
              </Stack>
            </Stack>
            <IconButton onClick={() => setShowDetails(false)}>
              <CloseIcon />
            </IconButton>
          </Stack>

          <Divider sx={{ mb: 3 }} />

          {/* Connection Status */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Connection Status
            </Typography>
            {renderConnectionStatus()}

            {qboLoading && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={connectionProgress}
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: "block" }}
                >
                  Establishing secure connection...
                </Typography>
              </Box>
            )}
          </Box>

          {/* Benefits Section */}
          {showBenefits && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Why Connect to QuickBooks?
              </Typography>
              <Stack spacing={2}>
                {connectionBenefits.map((benefit, index) => (
                  <Card key={index} sx={{ p: 2, backgroundColor: "grey.50" }}>
                    <Stack direction="row" spacing={2} alignItems="center">
                      {benefit.icon}
                      <Box>
                        <Typography variant="subtitle2" fontWeight={600}>
                          {benefit.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {benefit.description}
                        </Typography>
                      </Box>
                    </Stack>
                  </Card>
                ))}
              </Stack>
            </Box>
          )}

          {/* Connected Company Info */}
          {qboConnected && company?.qboCompanyName && (
            <Box sx={{ mb: 4 }}>
              <Alert severity="success" sx={{ borderRadius: 2 }}>
                <AlertTitle>
                  Fintuition Company Successfully Connected to QuickBooks
                </AlertTitle>
                <Typography variant="body2">
                  Connected to: <strong>{company.qboCompanyName}</strong>
                </Typography>
                {qboStatus?.qboRealmID && (
                  <Typography variant="caption" color="text.secondary">
                    Company ID: {qboStatus.qboRealmID}
                  </Typography>
                )}
              </Alert>
            </Box>
          )}

          {/* Action Buttons */}
          <Stack direction="row" spacing={2} justifyContent="flex-end">
            <Button variant="outlined" onClick={() => setShowDetails(false)}>
              Close
            </Button>

            {qboConnected ? (
              <>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={onRefreshStatus}
                  disabled={qboLoading}
                >
                  Refresh Status
                </Button>
                <Button
                  variant="contained"
                  color="error"
                  onClick={onDisconnect}
                  disabled={qboLoading}
                >
                  Disconnect
                </Button>
              </>
            ) : (
              <Box sx={{ position: "relative", display: "inline-block" }}>
                <Box
                  component="img"
                  src={qboButton}
                  alt="Connect to QuickBooks"
                  sx={{
                    cursor: "pointer",
                    height: 44,
                    width: "auto",
                    transition: "opacity 0.2s ease",
                    "&:hover": { opacity: 0 },
                    opacity: qboLoading ? 0.5 : 1,
                    pointerEvents: qboLoading ? "none" : "auto",
                  }}
                  onClick={!qboLoading ? onConnect : undefined}
                />
                <Box
                  component="img"
                  src={qboButtonHover}
                  alt="Connect to QuickBooks"
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    height: 44,
                    width: "auto",
                    opacity: 0,
                    transition: "opacity 0.2s ease",
                    "&:hover": { opacity: 1 },
                    pointerEvents: qboLoading ? "none" : "auto",
                  }}
                  onClick={!qboLoading ? onConnect : undefined}
                />
                {qboLoading && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "rgba(255, 255, 255, 0.8)",
                      borderRadius: 1,
                    }}
                  >
                    <CircularProgress size={20} />
                  </Box>
                )}
              </Box>
            )}
          </Stack>
        </Box>
      </Paper>
    </Slide>
  );

  return (
    <>
      {showDetails && (
        <Box
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0,0,0,0.5)",
            zIndex: 1200,
          }}
          onClick={() => setShowDetails(false)}
        />
      )}
      {showDetails && renderDetailedView()}
    </>
  );
};

export default QuickBooksConnection;
